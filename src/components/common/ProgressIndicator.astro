<div class="progress-scroll-container" aria-hidden="true">
  <div id="progress-scroll"></div>
</div>

<script>
  window.addEventListener("scroll", function () {
    updateProgressScroll();
  });
  window.addEventListener("load", function () {
    updateProgressScroll();
  });

  function updateProgressScroll() {
    if (document) {
      const progressScroll = document.getElementById("progress-scroll");
      if (progressScroll) {
        var scrollTop = window.scrollY || document.documentElement.scrollTop;
        var scrollHeight =
          document.documentElement.scrollHeight - window.innerHeight;
        var progress = (scrollTop / scrollHeight) * 100;
        progressScroll.style.width = progress + "%";
      }
    }
  }
</script>

<style>
  html:is(:not([data-has-hero]), [data-has-sidebar], [data-has-toc]) .progress-scroll-container {
    position: fixed;
    top: var(--sl-nav-height);
    left: 0;
    height: 0.25rem;
    width: 100%;
    background-color: transparent;
    z-index: 3;
  }

  #progress-scroll {
    height: 100%;
    width: 0px;
    background-color: var(--sl-color-text-accent);
  }
</style>

<style is:global>
  #starlight__on-this-page--mobile {
    margin-top: 0.25rem;
  }
</style>