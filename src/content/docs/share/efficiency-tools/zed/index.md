---
title: Zed
description: Zed
template: doc
lastUpdated: 2025-08-09 12:38:02
---

### 为什么会找到Zed
因为Visual Studio Code在我的电脑上太卡了，CPU经常能顶到近100%。我也不知道Visual Studio Code为什么会如此之卡，在网上也检索了很多资料，原因五花八门，总之没有解决导致电脑卡的问题。AI我也咨询过来,依然没有解决。


遂换到了Sublime Text,结果在Sublime Text中不能通过鼠标移动文件,很难受。在ChatGPT中咨询了相关Sublime Text有无相关插件可以支持,找到了一个FileManager插件,但还是不行，它是通过命令来的,不是通过鼠标来的。

> Sublime Text我在工作中用的比较多,因为工作的场景大多是文本的处理,不涉及的编程

最后我就让ChatGPT来推荐符合我要求的编辑器。ChatGPT推荐了好几个,最终选择了Zed

### Zed
官网: https://zed.dev/

开源: https://github.com/zed-industries/zed

### 非官方Windows版本
https://github.com/deevus/zed-windows-builds

每天从 main 分支拉取代码并构建发布

### 使用初体验
- 快! 我的老电脑终于不卡了。相比于Visual Studio Code来说,电脑再也没有卡过了。
- 界面太简洁了。非常喜欢❤️ 我就是喜欢简单、高效的工具
- 对命令行支持友好。如果我想在Sublime Text中使用命令行,还需要使用插件，关键是插件的质量也不行
- Free


> 我之前以为是电脑不行了,都想换电脑了,在使用了Zed后,我觉得可以不用换了。换电脑，成本有点太高了

### 2025.08.09
用了几天,Zed的体验超出我的预期。我比较喜欢的是以下几点:
- git
- terminal  会记住窗口,不像其他IDE每次打开，都要重新打开
- project manage

相比Visual Studio Code,虽然上述功能都有,甚至说Visual Studio Code更胜一筹，但是Visual Studio Code会让我的电脑风扇一直转，就很难受。Zed不会,使用Zed的过程,很安静

我之前使用Sublime Text,也很方便。但是Sublime Text的terminal是插件的形式,这么说吧，外面的插件很难用，即使是termius为Sublime Text做的插件。
